
package com.frt.usercore.common.enums.business;

import lombok.Data;
import lombok.Getter;

/**
 * 短信通道枚举类
 */
@Data
public enum SmsChannelTypeEnum {

    /**
     * 阿里云
     */
    ALI_YUN("ALI_YUN", "阿里云", "AliyunSmsServiceImpl"),

    /**
     * 专信云
     */
    ZHUAN_XIN_YUN("ZHUAN_XIN_YUN", "专信云", "ZhuanXinYunSmsServiceImpl");

    /**
     * 通道类型
     */
    private final String channelType;

    /**
     * 通道名称
     */
    private final String channelName;

    /**
     * 实现类名称
     */
    private final String implClassName;

    SmsChannelTypeEnum(String channelType, String channelName, String implClassName) {
        this.channelType = channelType;
        this.channelName = channelName;
        this.implClassName = implClassName;
    }

    /**
     * 根据code获取枚举
     *
     * @param channelType 通道类型
     * @return SmsChannelTypeEnum
     */
    public static SmsChannelTypeEnum getByChannelType(String channelType) {
        for (SmsChannelTypeEnum value : values()) {
            if (value.channelType.equals(channelType)) {
                return value;
            }
        }
        return null;
    }
}
